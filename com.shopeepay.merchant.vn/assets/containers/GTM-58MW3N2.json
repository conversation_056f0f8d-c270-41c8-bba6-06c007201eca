{"fingerprint": "NQ$0", "resource": {"version": "5", "macros": [{"function": "__e", "instance_name": "Event Name"}, {"function": "__md", "instance_name": "GA - Campaign Source", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "source"}, {"function": "__md", "instance_name": "GA - Campaign Medium", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "medium"}, {"function": "__md", "instance_name": "GA - Campaign Name", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "campaign"}, {"function": "__md", "instance_name": "GA - Campaign Keyword", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "term"}, {"function": "__md", "instance_name": "GA - Campaign Content", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "content"}, {"function": "__md", "instance_name": "GA - Google Click ID - ACLID", "vtp_setDefaultValue": false, "vtp_eventType": "CUSTOM", "vtp_key": "aclid"}, {"function": "__ai", "instance_name": "App ID"}, {"function": "__smm", "instance_name": "Google Analytics Property - Look Up", "vtp_setDefaultValue": true, "vtp_input": ["macro", 7], "vtp_defaultValue": "UA-125099498-1", "vtp_map": ["list", ["map", "key", "com.shopee.id", "value", "UA-61904553-13"], ["map", "key", "com.shopee.my", "value", "UA-61915055-11"], ["map", "key", "com.shopee.ph", "value", "UA-61918643-11"], ["map", "key", "com.shopee.sg", "value", "UA-61921742-13"], ["map", "key", "com.shopee.th", "value", "UA-61914165-11"], ["map", "key", "com.shopee.tw", "value", "UA-61915057-12"], ["map", "key", "com.shopee.vn", "value", "UA-61914164-11"]]}, {"function": "__gas", "instance_name": "GA - Settings - Campaign Tracking", "vtp_fieldsToSet": ["list", ["map", "fieldName", "screenName", "value", "campaign_details"], ["map", "fieldName", "&cs", "value", ["macro", 1]], ["map", "fieldName", "&cm", "value", ["macro", 2]], ["map", "fieldName", "&cn", "value", ["macro", 3]], ["map", "fieldName", "&ck", "value", ["macro", 4]], ["map", "fieldName", "&cc", "value", ["macro", 5]], ["map", "fieldName", "&gclid", "value", ["macro", 6]]], "vtp_collectAdid": true, "vtp_trackingId": ["macro", 8], "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseEcommerce": false}, {"function": "__md", "instance_name": "GA - Event Category", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "eventCategory"}, {"function": "__md", "instance_name": "GA - Screen Name", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "screenName"}, {"function": "__md", "instance_name": "GA - Login Type", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "dimension1"}, {"function": "__md", "instance_name": "GA - Device ID", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "dimension2"}, {"function": "__md", "instance_name": "GA - User ID", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "dimension3"}, {"function": "__md", "instance_name": "GA - Firebase Instance ID", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "dimension4"}, {"function": "__md", "instance_name": "GA - AppsFlyer ID", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "dimension5"}, {"function": "__cid", "instance_name": "Container ID"}, {"function": "__ctv", "instance_name": "Container Version"}, {"function": "__aid", "instance_name": "Advertiser ID"}, {"function": "__gas", "instance_name": "GA - Settings - Events Tracking", "vtp_fieldsToSet": ["list", ["map", "fieldName", "screenName", "value", ["macro", 11]]], "vtp_collectAdid": true, "vtp_dimension": ["list", ["map", "index", "1", "dimension", ["macro", 12]], ["map", "index", "2", "dimension", ["macro", 13]], ["map", "index", "3", "dimension", ["macro", 14]], ["map", "index", "4", "dimension", ["macro", 15]], ["map", "index", "5", "dimension", ["macro", 16]], ["map", "index", "6", "dimension", ["macro", 17]], ["map", "index", "7", "dimension", ["macro", 18]], ["map", "index", "8", "dimension", ["macro", 19]]], "vtp_trackingId": ["macro", 8], "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseEcommerce": false}, {"function": "__md", "instance_name": "GA - Event Action", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "eventAction"}, {"function": "__md", "instance_name": "GA - Event Label", "vtp_setDefaultValue": true, "vtp_defaultValue": "(not set)", "vtp_eventType": "CUSTOM", "vtp_key": "eventLabel"}, {"function": "__gas", "instance_name": "GA - Settings - Ecommerce Tracking", "vtp_collectAdid": true, "vtp_trackingId": ["macro", 8], "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseEcommerce": false}, {"function": "__an", "instance_name": "App Name"}, {"function": "__av", "instance_name": "App Version Code"}, {"function": "__l", "instance_name": "Language"}, {"function": "__ov", "instance_name": "OS Version"}, {"function": "__p", "instance_name": "Platform"}, {"function": "__sv", "instance_name": "SDK Version"}, {"function": "__dn", "instance_name": "Device Name"}, {"function": "__rs", "instance_name": "Screen Resolution"}, {"function": "__ate", "instance_name": "Advertiser Tracking Enabled"}, {"function": "__md", "instance_name": "Campaign Name", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "campaign"}, {"function": "__md", "instance_name": "Click ID (aclid)", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "aclid"}, {"function": "__md", "instance_name": "Campaign Click Timestamp", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "click_timestamp"}, {"function": "__md", "instance_name": "Campaign Content", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "content"}, {"function": "__md", "instance_name": "Campaign Custom Parameter 1", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "cp1"}, {"function": "__md", "instance_name": "Google Click ID (gclid)", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "gclid"}, {"function": "__md", "instance_name": "Campaign Source", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "source"}, {"function": "__md", "instance_name": "Campaign Search Term", "vtp_eventType": "SUGGESTED", "vtp_suggestedEvent": "firebase_campaign", "vtp_firebaseCampaignParam": "term"}, {"function": "__md", "instance_name": "Dynamic Link Accept Time", "vtp_key": "accept_time", "vtp_eventType": "CUSTOM"}, {"function": "__md", "instance_name": "Dynamic Link ID", "vtp_key": "link_id", "vtp_eventType": "CUSTOM"}], "tags": [{"function": "__ua", "once_per_event": true, "vtp_overrideGaSettings": false, "vtp_trackType": "TRACK_SCREENVIEW", "vtp_gaSettings": ["macro", 9], "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseCampaignData": true, "vtp_enableFirebaseEcommerce": true, "tag_id": 1}, {"function": "__ua", "once_per_event": true, "vtp_nonInteraction": false, "vtp_overrideGaSettings": false, "vtp_eventCategory": ["macro", 10], "vtp_trackType": "TRACK_EVENT", "vtp_gaSettings": ["macro", 20], "vtp_sendFirebaseCampaignData": false, "vtp_eventAction": ["macro", 21], "vtp_eventLabel": ["macro", 22], "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseCampaignData": true, "vtp_trackTypeIsEvent": true, "vtp_enableFirebaseEcommerce": true, "tag_id": 2}, {"function": "__ua", "once_per_event": true, "vtp_nonInteraction": false, "vtp_overrideGaSettings": true, "vtp_eventCategory": "tracking", "vtp_trackType": "TRACK_EVENT", "vtp_collectAdid": true, "vtp_readDataFrom": "FIREBASE_EVENT_DATA", "vtp_gaSettings": ["macro", 23], "vtp_sendFirebaseCampaignData": false, "vtp_eventAction": "add_to_cart", "vtp_eventLabel": "(not set)", "vtp_enableEcommerce": true, "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseCampaignData": true, "vtp_ecommerceIsEnabled": true, "vtp_trackTypeIsEvent": true, "vtp_enableFirebaseEcommerce": true, "tag_id": 3}, {"function": "__ua", "once_per_event": true, "vtp_nonInteraction": false, "vtp_overrideGaSettings": true, "vtp_eventCategory": "tracking", "vtp_trackType": "TRACK_EVENT", "vtp_collectAdid": true, "vtp_readDataFrom": "FIREBASE_EVENT_DATA", "vtp_gaSettings": ["macro", 23], "vtp_sendFirebaseCampaignData": false, "vtp_eventAction": "purchase", "vtp_eventLabel": "(not set)", "vtp_enableEcommerce": true, "vtp_enableRecaptchaOption": false, "vtp_enableUaRlsa": false, "vtp_enableUseInternalVersion": false, "vtp_enableFirebaseCampaignData": true, "vtp_ecommerceIsEnabled": true, "vtp_trackTypeIsEvent": true, "vtp_enableFirebaseEcommerce": true, "tag_id": 4}], "predicates": [{"function": "_cn", "arg0": ["macro", 0], "arg1": "campaign_detail"}, {"function": "_re", "arg0": ["macro", 0], "arg1": "login_event|signup_event"}, {"function": "_eq", "arg0": ["macro", 0], "arg1": "add_to_cart"}, {"function": "_eq", "arg0": ["macro", 0], "arg1": "ecommerce_purchase"}], "rules": [[["if", 0], ["add", 0]], [["if", 1], ["add", 1]], [["if", 2], ["add", 2]], [["if", 3], ["add", 3]]]}, "runtime": [[50, "__ctv_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "containerVersion", [7]]]], [50, "__ctv", [46, "data"], [36, ["__ctv_main", [15, "data"]]]], [50, "__ai_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationId", [7]]]], [50, "__ai", [46, "data"], [36, ["__ai_main", [15, "data"]]]], [50, "__an_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationName", [7]]]], [50, "__an", [46, "data"], [36, ["__an_main", [15, "data"]]]], [50, "__av_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "applicationVersion", [7]]]], [50, "__av", [46, "data"], [36, ["__av_main", [15, "data"]]]], [50, "__rs_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "resolution", [7]]]], [50, "__rs", [46, "data"], [36, ["__rs_main", [15, "data"]]]], [50, "__sv_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "sdkVersion", [7]]]], [50, "__sv", [46, "data"], [36, ["__sv_main", [15, "data"]]]], [50, "__e_main", [46], [41, "a", "b"], [3, "a", [2, [17, [15, "gtmUtils"], "mobile"], "event", [7]]], [22, [1, [1, [15, "a"], [18, [17, [15, "a"], "length"], 0]], [12, [16, [15, "a"], 0], "_"]], [46, [3, "b", [8, "_f", "first_open", "_v", "first_visit", "_iap", "in_app_purchase", "_e", "user_engagement", "_s", "session_start", "_au", "app_update", "_ui", "app_remove", "_ou", "os_update", "_cd", "app_clear_data", "_ae", "app_exception", "_nf", "notification_foreground", "_nr", "notification_receive", "_no", "notification_open", "_nd", "notification_dismiss", "_cmp", "firebase_campaign", "_vs", "screen_view", "_ar", "ad_reward"]], [3, "a", [30, [16, [15, "b"], [15, "a"]], [15, "a"]]]]], [36, [15, "a"]]], [50, "__e", [46, "data"], [36, ["__e_main", [15, "data"]]]], [50, "__l_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "language", [7]]]], [50, "__l", [46, "data"], [36, ["__l_main", [15, "data"]]]], [50, "__p_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "platform", [7]]]], [50, "__p", [46, "data"], [36, ["__p_main", [15, "data"]]]], [50, "__dn_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "deviceName", [7]]]], [50, "__dn", [46, "data"], [36, ["__dn_main", [15, "data"]]]], [50, "__ua_main", [46, "a"], [41, "b", "c", "d", "e", "f"], [43, [15, "a"], "fieldsToSet", ["__ua_convertFieldsToSet", [17, [15, "a"], "fieldsToSet"]]], ["__ua_convertIndexedField", [17, [15, "a"], "fieldsToSet"], [17, [15, "a"], "dimension"], "&cd", "index", "dimension"], ["__ua_convertIndexedField", [17, [15, "a"], "fieldsToSet"], [17, [15, "a"], "metric"], "&cm", "index", "metric"], ["__ua_convertIndexedField", [17, [15, "a"], "fieldsToSet"], [17, [15, "a"], "contentGroup"], "&cg", "index", "group"], [22, [17, [15, "a"], "gaSettings"], [46, [3, "b", [17, [15, "a"], "gaSettings"]], [43, [15, "b"], "fieldsToSet", ["__ua_convertFieldsToSet", [17, [15, "b"], "fieldsToSet"]]], ["__ua_convertIndexedField", [17, [15, "b"], "fieldsToSet"], [17, [15, "b"], "dimension"], "&cd", "index", "dimension"], ["__ua_convertIndexedField", [17, [15, "b"], "fieldsToSet"], [17, [15, "b"], "metric"], "&cm", "index", "metric"], ["__ua_convertIndexedField", [17, [15, "b"], "fieldsToSet"], [17, [15, "b"], "contentGroup"], "&cg", "index", "group"], [43, [15, "a"], "gaSettings", [45]], [3, "c", [2, [17, [15, "gtmUtils"], "common"], "copy", [7, [15, "b"], [44]]]], [3, "a", [2, [17, [15, "gtmUtils"], "common"], "copy", [7, [15, "a"], [15, "c"]]]]]], [3, "d", [17, [15, "a"], "fieldsToSet"]], [43, [15, "d"], "&tid", [17, [15, "a"], "trackingId"]], [3, "e", "screenview"], [3, "f", true], [38, [17, [15, "a"], "trackType"], [46, "TRACK_EVENT", "TRACK_EXCEPTION", "TRACK_SOCIAL", "TRACK_TIMING", "TRACK_TRANSACTION"], [46, [5, [46, [3, "e", "event"], [22, [21, [17, [15, "a"], "eventCategory"], [44]], [46, [43, [15, "d"], "&ec", [17, [15, "a"], "eventCategory"]]]], [22, [21, [17, [15, "a"], "eventAction"], [44]], [46, [43, [15, "d"], "&ea", [17, [15, "a"], "eventAction"]]]], [22, [21, [17, [15, "a"], "eventLabel"], [44]], [46, [43, [15, "d"], "&el", [17, [15, "a"], "eventLabel"]]]], [22, [21, [17, [15, "a"], "eventValue"], [44]], [46, [43, [15, "d"], "&ev", [17, [15, "a"], "eventValue"]]]], [22, [21, [17, [15, "a"], "nonInteraction"], [44]], [46, [43, [15, "d"], "&ni", [17, [15, "a"], "nonInteraction"]]]], [22, [17, [15, "a"], "enableFirebaseCampaignData"], [46, ["__ua_convertCampaignParams", [15, "d"], [17, [15, "a"], "sendFirebaseCampaignData"]]]], [4]]], [5, [46, [3, "e", "exception"], [22, [21, [17, [15, "a"], "exceptionDescription"], [44]], [46, [43, [15, "d"], "&exd", [17, [15, "a"], "exceptionDescription"]]]], [22, [21, [17, [15, "a"], "exceptionFatal"], [44]], [46, [43, [15, "d"], "&exf", [17, [15, "a"], "exceptionFatal"]]]], [4]]], [5, [46, [3, "e", "social"], [22, [21, [17, [15, "a"], "socialNetwork"], [44]], [46, [43, [15, "d"], "&sn", [17, [15, "a"], "socialNetwork"]]]], [22, [21, [17, [15, "a"], "socialAction"], [44]], [46, [43, [15, "d"], "&sa", [17, [15, "a"], "socialAction"]]]], [22, [21, [17, [15, "a"], "socialActionTarget"], [44]], [46, [43, [15, "d"], "&st", [17, [15, "a"], "socialActionTarget"]]]], [4]]], [5, [46, [3, "e", "timing"], [22, [21, [17, [15, "a"], "timingVar"], [44]], [46, [43, [15, "d"], "&utv", [17, [15, "a"], "timingVar"]]]], [22, [21, [17, [15, "a"], "timingCategory"], [44]], [46, [43, [15, "d"], "&utc", [17, [15, "a"], "timingCategory"]]]], [22, [21, [17, [15, "a"], "timingValue"], [44]], [46, [43, [15, "d"], "&utt", [17, [15, "a"], "timingValue"]]]], [22, [21, [17, [15, "a"], "timing<PERSON><PERSON><PERSON>"], [44]], [46, [43, [15, "d"], "&utl", [17, [15, "a"], "timing<PERSON><PERSON><PERSON>"]]]], [4]]], [5, [46, [3, "e", "transaction"], [3, "f", false], [4]]]]], [43, [15, "d"], "&t", [15, "e"]], [22, [1, [17, [15, "a"], "enableEcommerce"], [12, [17, [15, "a"], "readDataFrom"], "FIREBASE_EVENT_DATA"]], [46, [22, [28, ["__ua_sendEnhancedEcommerce", [15, "d"], [15, "a"]]], [46, [2, [17, [15, "gtmUtils"], "mobile"], "universalAnalytics", [7, [15, "d"], [15, "f"], [12, [15, "e"], "transaction"], [44], [44], false, false, [17, [15, "a"], "ecommerceVariableData"], [17, [15, "a"], "collectAdid"]]]]]], [46, [2, [17, [15, "gtmUtils"], "mobile"], "universalAnalytics", [7, [15, "d"], [15, "f"], [12, [15, "e"], "transaction"], [44], [44], [17, [15, "a"], "enableEcommerce"], false, [17, [15, "a"], "ecommerceVariableData"], [17, [15, "a"], "collectAdid"]]]]]], [50, "__ua_sendEnhancedEcommerce", [46, "a", "b"], [41, "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q"], [3, "c", [8, "select_content", "click", "view_item", "detail", "add_to_cart", "add", "remove_from_cart", "remove", "begin_checkout", "checkout", "checkout_progress", "checkout", "set_checkout_option", "checkout_option", "ecommerce_purchase", "purchase", "purchase_refund", "refund"]], [3, "d", [8, "view_item_list", "", "view_search_results", ""]], [3, "e", [8, "select_content", ""]], [3, "f", [8, "view_item", "", "view_item_list", "", "view_search_results", ""]], [3, "g", [8, "item_id", "id", "item_name", "name", "item_brand", "brand", "item_category", "category", "item_variant", "variant", "coupon", "coupon", "index", "position", "price", "price", "quantity", "quantity"]], [3, "h", [8, "transaction_id", "id", "affiliation", "affiliation", "coupon", "coupon", "item_list", "list", "checkout_option", "option", "value", "revenue", "tax", "tax", "shipping", "shipping", "checkout_step", "step"]], [3, "i", [8, "item_id", "id", "item_name", "name", "creative_name", "creative", "creative_slot", "position"]], [3, "j", [2, [17, [15, "gtmUtils"], "mobile"], "event", [7]]], [3, "k", [30, [2, [17, [15, "gtmUtils"], "mobile"], "eventParameters", [7]], [8]]], [3, "l", [8]], [3, "m", false], [43, [15, "l"], "currencyCode", [16, [15, "k"], "currency"]], [22, [2, [15, "k"], "hasOwnProperty", [7, "promotions"]], [46, [22, [2, [15, "e"], "hasOwnProperty", [7, [15, "j"]]], [46, [43, [15, "a"], "&t", "event"], [43, [15, "a"], "&ea", "click"], [22, [2, [15, "k"], "hasOwnProperty", [7, "content_type"]], [46, [43, [15, "a"], "&ec", [16, [15, "k"], "content_type"]]]], [22, [2, [15, "k"], "hasOwnProperty", [7, "item_id"]], [46, [43, [15, "a"], "&el", [16, [15, "k"], "item_id"]]]], [43, [15, "l"], "promoClick", ["__ua_convertPromotions", [15, "k"], [15, "i"]]], [3, "m", true]], [46, [22, [2, [15, "f"], "hasOwnProperty", [7, [15, "j"]]], [46, [43, [15, "l"], "promoView", ["__ua_convertPromotions", [15, "k"], [15, "i"]]], [3, "m", true]]]]]], [46, [22, [2, [15, "c"], "hasOwnProperty", [7, [15, "j"]]], [46, [22, [12, [15, "j"], "set_checkout_option"], [46, [43, [15, "a"], "&t", "event"], [43, [15, "a"], "&ea", "Option"], [43, [15, "a"], "&ec", "Checkout"]]], [3, "n", [8]], [22, [2, [15, "k"], "hasOwnProperty", [7, "items"]], [46, [43, [15, "n"], "products", ["__ua_convertProductList", [15, "k"], [15, "g"], false]]]], [3, "o", [8]], [3, "p", false], [47, "q", [15, "h"], [46, [22, [2, [15, "k"], "hasOwnProperty", [7, [15, "q"]]], [46, [43, [15, "o"], [16, [15, "h"], [15, "q"]], [16, [15, "k"], [15, "q"]]], [3, "p", true]]]]], [22, [15, "p"], [46, [43, [15, "n"], "actionField", [15, "o"]]]], [43, [15, "l"], [16, [15, "c"], [15, "j"]], [15, "n"]], [3, "m", true]], [46, [22, [2, [15, "d"], "hasOwnProperty", [7, [15, "j"]]], [46, [43, [15, "l"], "impressions", ["__ua_convertProductList", [15, "k"], [15, "g"], true]], [3, "m", true]]]]]]], [22, [15, "m"], [46, [2, [17, [15, "gtmUtils"], "mobile"], "universalAnalytics", [7, [15, "a"], false, false, [44], [44], [17, [15, "b"], "enableEcommerce"], false, [15, "l"], [17, [15, "b"], "collectAdid"]]]]], [36, [15, "m"]]], [50, "__ua_convertScionFields", [46, "a", "b"], [41, "c", "d", "e"], [22, [30, [12, [15, "b"], [44]], [12, [15, "b"], [45]]], [46, [36, [8]]]], [3, "c", [8]], [47, "d", [15, "b"], [46, [22, [30, [12, [2, [15, "d"], "indexOf", [7, "dimension"]], 0], [12, [2, [15, "d"], "indexOf", [7, "metric"]], 0]], [46, [43, [15, "c"], [15, "d"], [16, [15, "b"], [15, "d"]]]]]]], [47, "e", [15, "a"], [46, [22, [2, [15, "b"], "hasOwnProperty", [7, [15, "e"]]], [46, [43, [15, "c"], [16, [15, "a"], [15, "e"]], [16, [15, "b"], [15, "e"]]]]]]], [36, [15, "c"]]], [50, "__ua_convertPromotions", [46, "a", "b"], [41, "c", "d", "e"], [3, "c", [7]], [3, "d", [16, [15, "a"], "promotions"]], [3, "e", 0], [42, [23, [15, "e"], [17, [15, "d"], "length"]], [33, [15, "e"], [3, "e", [0, [15, "e"], 1]]], false, [46, [2, [15, "c"], "push", [7, ["__ua_convertScionFields", [15, "b"], [16, [15, "d"], [15, "e"]]]]]]], [36, [8, "promotions", [15, "c"]]]], [50, "__ua_convertProductList", [46, "a", "b", "c"], [41, "d", "e", "f", "g"], [3, "d", [16, [15, "a"], "items"]], [3, "e", [7]], [22, [28, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "d"]]]], [46, [2, [15, "e"], "push", [7, ["__ua_convertScionFields", [15, "b"], [15, "d"]]]]], [46, [3, "f", 0], [42, [23, [15, "f"], [17, [15, "d"], "length"]], [33, [15, "f"], [3, "f", [0, [15, "f"], 1]]], false, [46, [3, "g", ["__ua_convertScionFields", [15, "b"], [16, [15, "d"], [15, "f"]]]], [22, [1, [15, "c"], [2, [15, "a"], "hasOwnProperty", [7, "item_list"]]], [46, [43, [15, "g"], "list", [16, [15, "a"], "item_list"]]]], [2, [15, "e"], "push", [7, [15, "g"]]]]]]], [36, [15, "e"]]], [50, "__ua_convertFieldsToSet", [46, "a"], [41, "b", "c", "d", "e", "f", "g"], [22, [28, [15, "a"]], [46, [36, [8]]]], [3, "b", [8, "account", "&tid", "anonymizeIp", "&aip", "appName", "&an", "appVersion", "&av", "contentDescription", "&cd", "contentGroup", "&cg", "dimension", "&cd", "eventAction", "&ea", "eventCategory", "&ec", "eventLabel", "&el", "eventValue", "&ev", "exceptionDescription", "&exd", "exceptionFatal", "&exf", "firebaseCampaignAdNetworkClickId", "&aclid", "firebaseCampaignAdNetworkId", "&anid", "firebaseCampaignContent", "&cc", "firebaseCampaignGoogleClickId", "&gclid", "firebaseCampaignMedium", "&cm", "firebaseCampaignName", "&cn", "firebaseCampaignSource", "&cs", "firebaseCampaignTerm", "&ck", "metric", "&cm", "noninteraction", "&ni", "page", "&dp", "referrer", "&dr", "sampleRate", "&sf", "screenName", "&cd", "sessionControl", "&sc", "socialAction", "&sa", "socialActionTarget", "&st", "socialNetwork", "&sn", "timingCategory", "&utc", "timing<PERSON><PERSON><PERSON>", "&utl", "timingValue", "&utt", "timingVar", "&utv", "title", "&dt", "userId", "&uid"]], [3, "c", [8]], [3, "d", 0], [42, [23, [15, "d"], [17, [15, "a"], "length"]], [33, [15, "d"], [3, "d", [0, [15, "d"], 1]]], false, [46, [3, "e", [16, [16, [15, "a"], [15, "d"]], "fieldName"]], [3, "f", [16, [16, [15, "a"], [15, "d"]], "value"]], [22, [1, [21, [15, "f"], [44]], [21, [15, "f"], [45]]], [46, [22, [12, [2, [15, "e"], "char<PERSON>t", [7, 0]], "&"], [46, [43, [15, "c"], [15, "e"], [15, "f"]]], [46, [3, "g", [16, [15, "b"], [15, "e"]]], [22, [21, [15, "g"], [44]], [46, [43, [15, "c"], [15, "g"], [15, "f"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", [0, "Dropping the invalid UA tag field name ", [15, "e"]]]]]]]]]]]], [36, [15, "c"]]], [50, "__ua_convertIndexedField", [46, "a", "b", "c", "d", "e"], [41, "f", "g"], [22, [15, "b"], [46, [3, "f", 0], [42, [23, [15, "f"], [17, [15, "b"], "length"]], [33, [15, "f"], [3, "f", [0, [15, "f"], 1]]], false, [46, [3, "g", [16, [15, "b"], [15, "f"]]], [22, [1, [1, [15, "g"], [21, [16, [15, "g"], [15, "e"]], [44]]], [21, [16, [15, "g"], [15, "e"]], [45]]], [46, [43, [15, "a"], [0, [15, "c"], [16, [15, "g"], [15, "d"]]], [16, [15, "g"], [15, "e"]]]]]]]]]], [50, "__ua_convertCampaignParams", [46, "a", "b"], [41, "c", "d", "e"], [22, [21, [2, [17, [15, "gtmUtils"], "mobile"], "event", [7]], "_cmp"], [46, [36]]], [3, "c", [30, [2, [17, [15, "gtmUtils"], "mobile"], "eventParameters", [7]], [8]]], [22, [15, "b"], [46, [3, "d", [8, "content", "&cc", "anid", "&anid", "medium", "&cm", "campaign", "&cn", "source", "&cs", "term", "&ck", "gclid", "&gclid", "aclid", "&aclid"]]], [46, [3, "d", [8, "anid", "&anid", "gclid", "&gclid", "aclid", "&aclid"]]]], [47, "e", [15, "d"], [46, [22, [1, [16, [15, "c"], [15, "e"]], [28, [2, [15, "a"], "hasOwnProperty", [7, [16, [15, "d"], [15, "e"]]]]]], [46, [43, [15, "a"], [16, [15, "d"], [15, "e"]], [16, [15, "c"], [15, "e"]]]]]]]], [50, "__ua", [46, "data"], [36, ["__ua_main", [15, "data"]]]], [50, "__ate_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "advertiserTrackingEnabled", [7]]]], [50, "__ate", [46, "data"], [36, ["__ate_main", [15, "data"]]]], [50, "__cid_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "containerId", [7]]]], [50, "__cid", [46, "data"], [36, ["__cid_main", [15, "data"]]]], [50, "__gas_main", [46, "a"], [36, [15, "a"]]], [50, "__gas", [46, "data"], [36, ["__gas_main", [15, "data"]]]], [50, "__aid_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "advertiserId", [7]]]], [50, "__aid", [46, "data"], [36, ["__aid_main", [15, "data"]]]], [50, "__smm_main", [46, "a"], [41, "b", "c"], [3, "b", [30, [2, [17, [15, "gtmUtils"], "common"], "tableToMap", [7, [17, [15, "a"], "map"], "key", "value"]], [8]]], [3, "c", [17, [15, "a"], "input"]], [36, [39, [2, [15, "b"], "hasOwnProperty", [7, [15, "c"]]], [16, [15, "b"], [15, "c"]], [17, [15, "a"], "defaultValue"]]]], [50, "__smm", [46, "data"], [36, ["__smm_main", [15, "data"]]]], [50, "__md_main", [46, "a"], [41, "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l"], [22, [12, [17, [15, "a"], "eventType"], "CUSTOM"], [46, [3, "b", [16, [15, "a"], "key"]]], [46, [22, [12, [17, [15, "a"], "eventType"], "SUGGESTED"], [46, [47, "c", [15, "a"], [46, [3, "d", [2, [15, "c"], "lastIndexOf", [7, "Param"]]], [22, [1, [29, [15, "d"], [27, 1]], [12, [15, "d"], [37, [17, [15, "c"], "length"], 5]]], [46, [22, [20, [15, "b"], [44]], [46, [3, "b", [16, [15, "a"], [15, "c"]]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", [0, [0, [0, "Ignoring unexpected additional parameter ", "key in the data (key = \""], [15, "c"]], "\")."]]]]]]]]]], [46, [22, [20, [17, [15, "a"], "eventType"], [44]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", "Missing expected eventType param"]], [3, "b", [16, [15, "a"], "key"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", [0, "Unexpected eventType param value: ", [17, [15, "a"], "eventType"]]]], [36]]]]]]], [22, [20, [15, "b"], [44]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", "No parameter key specified in the data."]], [36]]], [3, "e", [30, [2, [17, [15, "gtmUtils"], "mobile"], "eventParameters", [7]], [8]]], [22, [12, [2, [15, "b"], "indexOf", [7, "."]], [27, 1]], [46, [3, "f", [16, [15, "e"], [15, "b"]]], [22, [1, [1, [20, [15, "f"], [44]], [12, [17, [15, "a"], "eventType"], "SUGGESTED"]], [17, [15, "a"], "checkEcommerceValue"]], [46, [3, "g", [7, "items", "promotions"]], [47, "h", [15, "g"], [46, [3, "i", [16, [15, "g"], [15, "h"]]], [22, [21, [16, [15, "e"], [15, "i"]], [44]], [46, [3, "j", [16, [15, "e"], [15, "i"]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "j"]]], [46, [22, [21, [16, [16, [15, "j"], 0], [15, "b"]], [44]], [46, [36, [16, [16, [15, "j"], 0], [15, "b"]]]]]], [46, [22, [21, [16, [15, "j"], [15, "b"]], [44]], [46, [36, [16, [15, "j"], [15, "b"]]]]]]]]]]]]]], [46, [3, "k", [2, [17, [15, "gtmUtils"], "common"], "split", [7, [15, "b"], "."]]], [3, "f", [15, "e"]], [47, "l", [15, "k"], [46, [3, "f", [16, [15, "f"], [16, [15, "k"], [15, "l"]]]], [22, [20, [15, "f"], [44]], [46, [4]]]]]]], [22, [21, [15, "f"], [44]], [46, [36, [15, "f"]]], [46, [22, [21, [17, [15, "a"], "defaultValue"], [44]], [46, [36, [17, [15, "a"], "defaultValue"]]], [46, [2, [17, [15, "gtmUtils"], "common"], "log", [7, "w", [0, [0, "Event does not have parameter \"", [15, "b"]], "\" and no default value was defined. Returning \"undefined\"."]]]]]]]], [50, "__md", [46, "data"], [36, ["__md_main", [15, "data"]]]], [50, "__ov_main", [46], [36, [2, [17, [15, "gtmUtils"], "mobile"], "osVersion", [7]]]], [50, "__ov", [46, "data"], [36, ["__ov_main", [15, "data"]]]], [50, "main", [46, "a"], [43, [17, [15, "a"], "common"], "tableToMap", [15, "tableToMap"]], [43, [17, [15, "a"], "common"], "stringify", [15, "stringify"]], [43, [17, [15, "a"], "common"], "copy", [15, "copy"]], [43, [17, [15, "a"], "common"], "split", [15, "split"]]], [50, "tableToMap", [46, "a", "b", "c"], [41, "d", "e", "f"], [3, "d", [8]], [3, "e", false], [3, "f", 0], [42, [1, [15, "a"], [23, [15, "f"], [17, [15, "a"], "length"]]], [33, [15, "f"], [3, "f", [0, [15, "f"], 1]]], false, [46, [22, [1, [1, [16, [15, "a"], [15, "f"]], [2, [16, [15, "a"], [15, "f"]], "hasOwnProperty", [7, [15, "b"]]]], [2, [16, [15, "a"], [15, "f"]], "hasOwnProperty", [7, [15, "c"]]]], [46, [43, [15, "d"], [16, [16, [15, "a"], [15, "f"]], [15, "b"]], [16, [16, [15, "a"], [15, "f"]], [15, "c"]]], [3, "e", true]]]]], [36, [39, [15, "e"], [15, "d"], [45]]]], [50, "stringify", [46, "a"], [41, "b", "c", "d", "e"], [22, [20, [15, "a"], [45]], [46, [36, "null"]]], [22, [20, [15, "a"], [44]], [46, [36, [44]]]], [22, [30, [12, [40, [15, "a"]], "number"], [12, [40, [15, "a"]], "boolean"]], [46, [36, [2, [15, "a"], "toString", [7]]]]], [22, [12, [40, [15, "a"]], "string"], [46, [36, [0, [0, "\"", [2, ["split", [15, "a"], "\""], "join", [7, "\\\""]]], "\""]]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "a"]]], [46, [3, "b", [7]], [3, "c", 0], [42, [23, [15, "c"], [17, [15, "a"], "length"]], [33, [15, "c"], [3, "c", [0, [15, "c"], 1]]], false, [46, [3, "d", ["stringify", [16, [15, "a"], [15, "c"]]]], [22, [12, [15, "d"], [44]], [46, [2, [15, "b"], "push", [7, "null"]]], [46, [2, [15, "b"], "push", [7, [15, "d"]]]]]]], [36, [0, [0, "[", [2, [15, "b"], "join", [7, ","]]], "]"]]]], [22, [12, [40, [15, "a"]], "object"], [46, [3, "b", [7]], [47, "e", [15, "a"], [46, [3, "d", ["stringify", [16, [15, "a"], [15, "e"]]]], [22, [29, [15, "d"], [44]], [46, [2, [15, "b"], "push", [7, [0, [0, [0, "\"", [15, "e"]], "\":"], [15, "d"]]]]]]]], [36, [0, [0, "{", [2, [15, "b"], "join", [7, ","]]], "}"]]]], [2, [17, [15, "gtmUtils"], "common"], "log", [7, "e", "Attempting to stringify unknown type!"]], [36, [44]]], [50, "split", [46, "a", "b"], [41, "c", "d", "e", "f"], [3, "c", [7]], [22, [20, [15, "b"], ""], [46, [3, "d", [17, [15, "a"], "length"]], [3, "e", 0], [42, [23, [15, "e"], [15, "d"]], [33, [15, "e"], [3, "e", [0, [15, "e"], 1]]], false, [46, [2, [15, "c"], "push", [7, [16, [15, "a"], [15, "e"]]]]]], [36, [15, "c"]]]], [42, [1, [15, "a"], [19, [2, [15, "a"], "indexOf", [7, [15, "b"]]], 0]], [46], false, [46, [3, "f", [2, [15, "a"], "indexOf", [7, [15, "b"]]]], [22, [12, [15, "f"], 0], [46, [2, [15, "c"], "push", [7, ""]]], [46, [2, [15, "c"], "push", [7, [2, [15, "a"], "substring", [7, 0, [15, "f"]]]]]]], [3, "a", [2, [15, "a"], "substring", [7, [0, [15, "f"], [17, [15, "b"], "length"]]]]]]], [2, [15, "c"], "push", [7, [15, "a"]]], [36, [15, "c"]]], [50, "copy", [46, "a", "b"], [41, "c", "d"], [3, "b", [30, [15, "b"], [39, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "a"]]], [7], [8]]]], [47, "c", [15, "a"], [46, [3, "d", [16, [15, "a"], [15, "c"]]], [22, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [15, "d"]]], [46, [22, [28, [2, [17, [15, "gtmUtils"], "common"], "isArray", [7, [16, [15, "b"], [15, "c"]]]]], [46, [43, [15, "b"], [15, "c"], [7]]]], [43, [15, "b"], [15, "c"], ["copy", [15, "d"], [16, [15, "b"], [15, "c"]]]]], [46, [22, [1, [29, [15, "d"], [45]], [12, [40, [15, "d"]], "object"]], [46, [22, [29, [40, [16, [15, "b"], [15, "c"]]], "object"], [46, [43, [15, "b"], [15, "c"], [8]]]], [43, [15, "b"], [15, "c"], ["copy", [15, "d"], [16, [15, "b"], [15, "c"]]]]], [46, [43, [15, "b"], [15, "c"], [15, "d"]]]]]]]], [36, [15, "b"]]]]}